# NextChat 技术栈文档

## 前端技术栈

### 核心框架
- **Next.js** - React 全栈框架，支持 SSR/SSG
- **React** - 用户界面构建库
- **TypeScript** - 类型安全的 JavaScript 超集

### UI 组件库
- **Tailwind CSS** - 原子化 CSS 框架
- **Radix UI** - 无样式的可访问组件库
- **Lucide React** - 图标库

### 状态管理
- **Zustand** - 轻量级状态管理库
- **React Context** - React 内置状态管理

### 样式处理
- **PostCSS** - CSS 后处理器
- **Autoprefixer** - 自动添加 CSS 前缀
- **CSS Modules** - 模块化 CSS

## 后端技术栈

### API 路由
- **Next.js API Routes** - 服务端 API 处理
- **Edge Runtime** - 边缘计算运行时

### 数据库
- **MySQL** - 关系型数据库
- **Prisma** - 数据库 ORM（如果使用）

### 认证授权
- **JWT** - JSON Web Token 认证
- **环境变量加密** - API Key 安全存储

## 桌面应用

### 跨平台框架
- **Tauri** - Rust + Web 技术的桌面应用框架
- **Rust** - 系统级编程语言，用于 Tauri 后端

### 平台支持
- Windows
- macOS  
- Linux

## AI 集成

### 支持的 AI 模型
- **OpenAI GPT** (GPT-3.5, GPT-4)
- **Claude** (Anthropic)
- **Gemini Pro** (Google)
- **DeepSeek**
- **SiliconFlow**
- **302.AI**

### 协议支持
- **MCP (Model Context Protocol)** - 模型上下文协议
- **Realtime Chat** - 实时对话功能
- **Streaming Response** - 流式响应

## 开发工具

### 构建工具
- **Webpack** - 模块打包器（Next.js 内置）
- **SWC** - 快速 JavaScript/TypeScript 编译器
- **Turbopack** - 下一代打包器（可选）

### 代码质量
- **ESLint** - JavaScript/TypeScript 代码检查
- **Prettier** - 代码格式化
- **Husky** - Git hooks 管理
- **TypeScript** - 静态类型检查

### 包管理
- **Yarn** - 包管理器
- **npm** - 备选包管理器

## 部署方案

### 云平台部署
- **Vercel** - 首选部署平台，支持一键部署
- **Netlify** - 备选静态站点部署
- **Cloudflare Pages** - 边缘计算部署

### 容器化部署
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排

### 自托管部署
- **Node.js** - 运行时环境
- **PM2** - 进程管理器
- **Nginx** - 反向代理服务器

## 特色功能技术

### PWA 支持
- **Service Worker** - 离线缓存
- **Web App Manifest** - 应用清单
- **Push Notifications** - 推送通知

### 国际化
- **i18n** - 多语言支持
- 支持语言：中文、英文、日文、韩文等

### 数据存储
- **LocalStorage** - 浏览器本地存储
- **IndexedDB** - 客户端数据库
- **Session Storage** - 会话存储

### 安全特性
- **CSP (Content Security Policy)** - 内容安全策略
- **CORS** - 跨域资源共享配置
- **环境变量隔离** - 敏感信息保护

## 性能优化

### 代码分割
- **Dynamic Imports** - 动态导入
- **Route-based Splitting** - 路由级代码分割
- **Component Lazy Loading** - 组件懒加载

### 缓存策略
- **HTTP 缓存** - 浏览器缓存
- **CDN 缓存** - 内容分发网络
- **Memory 缓存** - 内存缓存

### 图片优化
- **Next.js Image** - 自动图片优化
- **WebP 支持** - 现代图片格式
- **响应式图片** - 自适应尺寸

## 监控与分析

### 错误监控
- **Sentry** - 错误追踪（可选）
- **Console Logging** - 控制台日志

### 性能监控
- **Web Vitals** - 核心网页指标
- **Lighthouse** - 性能审计

## 开发环境要求

### 系统要求
- **Node.js** >= 18
- **Yarn** 包管理器
- **Docker** >= 20（容器部署）

### 开发工具推荐
- **VS Code** - 推荐编辑器
- **Chrome DevTools** - 调试工具
- **React Developer Tools** - React 调试扩展

## 环境配置

### 必需环境变量
```bash
OPENAI_API_KEY=<your-api-key>
CODE=<access-password>
BASE_URL=<api-base-url>
```

### 可选环境变量
```bash
ENABLE_MCP=true
STABILITY_API_KEY=<stability-key>
SILICONFLOW_API_KEY=<siliconflow-key>
AI302_API_KEY=<302ai-key>
```

## 项目架构

### 目录结构
```
NextChat/
├── app/                          # Next.js App Router 目录
│   ├── api/                      # API 路由
│   │   ├── openai/              # OpenAI API 代理
│   │   ├── anthropic/           # Claude API 代理
│   │   ├── google/              # Gemini API 代理
│   │   ├── deepseek/            # DeepSeek API 代理
│   │   ├── siliconflow/         # SiliconFlow API 代理
│   │   ├── 302ai/               # 302.AI API 代理
│   │   ├── azure/               # Azure OpenAI API 代理
│   │   ├── baidu/               # 百度文心一言 API
│   │   ├── bytedance/           # 字节跳动豆包 API
│   │   ├── alibaba/             # 阿里通义千问 API
│   │   ├── tencent/             # 腾讯混元 API
│   │   ├── moonshot/            # 月之暗面 Kimi API
│   │   ├── iflytek/             # 科大讯飞星火 API
│   │   ├── xai/                 # xAI Grok API
│   │   ├── chatglm/             # 智谱 ChatGLM API
│   │   ├── stability/           # Stability AI 图像生成
│   │   ├── artifacts/           # 代码工件功能
│   │   └── cache/               # 缓存和文件上传
│   ├── client/                   # 客户端 API 封装
│   │   ├── api.ts               # 统一 API 接口
│   │   └── platforms/           # 各平台 API 实现
│   │       ├── openai.ts        # OpenAI 平台实现
│   │       ├── anthropic.ts     # Anthropic 平台实现
│   │       ├── google.ts        # Google 平台实现
│   │       └── ...              # 其他平台实现
│   ├── components/              # React 组件
│   │   ├── ui/                  # 基础 UI 组件
│   │   ├── chat/                # 聊天相关组件
│   │   ├── settings/            # 设置相关组件
│   │   ├── mask/                # 面具相关组件
│   │   └── artifacts/           # 代码工件组件
│   ├── config/                  # 配置文件
│   │   ├── client.ts            # 客户端配置
│   │   └── server.ts            # 服务端配置
│   ├── constant.ts              # 常量定义
│   ├── locales/                 # 国际化语言文件
│   │   ├── cn.ts                # 中文
│   │   ├── en.ts                # 英文
│   │   ├── jp.ts                # 日文
│   │   └── ...                  # 其他语言
│   ├── masks/                   # 面具系统
│   │   ├── build.ts             # 面具构建脚本
│   │   └── typing.ts            # 面具类型定义
│   ├── mcp/                     # MCP (Model Context Protocol)
│   │   ├── actions.ts           # MCP 服务端操作
│   │   ├── client.ts            # MCP 客户端
│   │   ├── logger.ts            # MCP 日志
│   │   ├── types.ts             # MCP 类型定义
│   │   └── mcp_config.json      # MCP 配置文件
│   ├── store/                   # 状态管理 (Zustand)
│   │   ├── access.ts            # 访问控制状态
│   │   ├── chat.ts              # 聊天状态
│   │   ├── config.ts            # 配置状态
│   │   ├── mask.ts              # 面具状态
│   │   ├── plugin.ts            # 插件状态
│   │   └── update.ts            # 更新状态
│   ├── utils/                   # 工具函数
│   │   ├── chat.ts              # 聊天相关工具
│   │   ├── model.ts             # 模型相关工具
│   │   ├── store.ts             # 状态持久化工具
│   │   ├── cloudflare.ts        # Cloudflare 工具
│   │   └── clone.ts             # 对象克隆工具
│   ├── typing.ts                # 全局类型定义
│   ├── layout.tsx               # 根布局组件
│   ├── page.tsx                 # 首页组件
│   ├── globals.css              # 全局样式
│   └── [chat]/                  # 动态路由页面
├── components/                   # 共享组件目录
├── docs/                        # 项目文档
│   ├── README.md                # 项目说明
│   ├── CHANGELOG.md             # 更新日志
│   └── deployment/              # 部署相关文档
├── public/                      # 静态资源
│   ├── icons/                   # 应用图标
│   ├── locales/                 # 本地化资源
│   ├── masks/                   # 面具资源
│   ├── serviceWorker.js         # PWA Service Worker
│   └── manifest.json            # PWA 应用清单
├── scripts/                     # 构建和工具脚本
│   ├── setup.sh                # 环境设置脚本
│   ├── fetch-prompts.mjs        # 提示词获取脚本
│   └── build/                   # 构建相关脚本
├── src-tauri/                   # Tauri 桌面应用
│   ├── src/                     # Rust 源码
│   │   ├── main.rs              # 主入口
│   │   └── stream.rs            # 流处理模块
│   ├── icons/                   # 桌面应用图标
│   ├── Cargo.toml               # Rust 项目配置
│   ├── Cargo.lock               # Rust 依赖锁定
│   └── tauri.conf.json          # Tauri 配置
├── styles/                      # 样式文件
│   ├── globals.css              # 全局样式
│   ├── markdown.scss            # Markdown 样式
│   ├── window.scss              # 窗口样式
│   └── components/              # 组件样式
├── .env.example                 # 环境变量示例
├── .env.local                   # 本地环境变量 (需创建)
├── .gitignore                   # Git 忽略文件
├── .dockerignore                # Docker 忽略文件
├── Dockerfile                   # Docker 构建文件
├── docker-compose.yml           # Docker Compose 配置
├── next.config.mjs              # Next.js 配置
├── tailwind.config.ts           # Tailwind CSS 配置
├── tsconfig.json                # TypeScript 配置
├── package.json                 # 项目依赖和脚本
├── yarn.lock                    # Yarn 依赖锁定
├── README.md                    # 项目说明文档
├── README_CN.md                 # 中文说明文档
└── tech.md                      # 技术栈文档
```

### 核心模块详解

#### 1. Chat Engine (对话引擎)
- **位置**: `app/client/api.ts`, `app/store/chat.ts`
- **功能**: 统一的聊天接口，支持多种 AI 模型
- **特性**: 流式响应、消息历史、上下文管理

#### 2. Model Manager (模型管理)
- **位置**: `app/utils/model.ts`, `app/store/config.ts`
- **功能**: 模型配置、切换、参数管理
- **支持**: 15+ AI 服务提供商

#### 3. Mask System (面具系统)
- **位置**: `app/masks/`, `app/store/mask.ts`
- **功能**: 预设角色和提示词模板
- **特性**: 自定义面具、内置面具库

#### 4. Plugin System (插件系统)
- **位置**: `app/store/plugin.ts`
- **功能**: 扩展功能支持
- **特性**: 动态加载、API 集成

#### 5. Settings Manager (设置管理)
- **位置**: `app/store/config.ts`, `app/store/access.ts`
- **功能**: 用户配置、访问控制、主题设置
- **特性**: 持久化存储、实时同步

#### 6. MCP Integration (MCP 集成)
- **位置**: `app/mcp/`
- **功能**: Model Context Protocol 支持
- **特性**: 工具调用、上下文共享

#### 7. Artifacts System (代码工件)
- **位置**: `app/api/artifacts/`, `app/components/artifacts/`
- **功能**: 代码生成、预览、执行
- **特性**: 实时预览、多语言支持

#### 8. PWA Support (PWA 支持)
- **位置**: `public/serviceWorker.js`, `public/manifest.json`
- **功能**: 离线使用、桌面安装
- **特性**: 缓存策略、推送通知

#### 9. Desktop App (桌面应用)
- **位置**: `src-tauri/`
- **技术**: Tauri + Rust
- **平台**: Windows, macOS, Linux
- **特性**: 原生性能、系统集成

## 版本控制

### Git 工作流
- **主分支**: main
- **功能分支**: feature/*
- **修复分支**: fix/*
- **发布分支**: release/*

### 发布策略
- **语义化版本** - SemVer 规范
- **自动化发布** - GitHub Actions
- **多平台构建** - 跨平台支持

